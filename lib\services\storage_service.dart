import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alarm_settings_model.dart';
import '../models/bluetooth_device_model.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  // Storage keys
  static const String _alarmSettingsKey = 'alarm_settings';
  static const String _savedDevicesKey = 'saved_devices';
  static const String _appSettingsKey = 'app_settings';

  // Initialize storage service
  Future<bool> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      return true;
    } catch (e) {
      debugPrint('Error initializing storage service: $e');
      return false;
    }
  }

  // Ensure preferences are initialized
  Future<void> _ensureInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Save alarm settings
  Future<void> saveAlarmSettings(List<AlarmSettings> alarmSettings) async {
    try {
      await _ensureInitialized();
      
      List<Map<String, dynamic>> jsonList = alarmSettings
          .map((settings) => settings.toJson())
          .toList();
      
      String jsonString = jsonEncode(jsonList);
      await _prefs!.setString(_alarmSettingsKey, jsonString);
      
      debugPrint('Saved ${alarmSettings.length} alarm settings');
    } catch (e) {
      debugPrint('Error saving alarm settings: $e');
    }
  }

  // Get alarm settings
  Future<List<AlarmSettings>> getAlarmSettings() async {
    try {
      await _ensureInitialized();
      
      String? jsonString = _prefs!.getString(_alarmSettingsKey);
      if (jsonString == null) return [];
      
      List<dynamic> jsonList = jsonDecode(jsonString);
      List<AlarmSettings> alarmSettings = jsonList
          .map((json) => AlarmSettings.fromJson(json))
          .toList();
      
      debugPrint('Loaded ${alarmSettings.length} alarm settings');
      return alarmSettings;
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
      return [];
    }
  }

  // Save a single alarm setting
  Future<void> saveAlarmSetting(AlarmSettings settings) async {
    try {
      List<AlarmSettings> currentSettings = await getAlarmSettings();
      
      // Update existing or add new
      int existingIndex = currentSettings.indexWhere(
        (alarm) => alarm.deviceAddress == settings.deviceAddress && alarm.type == settings.type,
      );
      
      if (existingIndex != -1) {
        currentSettings[existingIndex] = settings;
      } else {
        currentSettings.add(settings);
      }
      
      await saveAlarmSettings(currentSettings);
    } catch (e) {
      debugPrint('Error saving single alarm setting: $e');
    }
  }

  // Delete alarm setting
  Future<void> deleteAlarmSetting(String deviceAddress, AlarmType type) async {
    try {
      List<AlarmSettings> currentSettings = await getAlarmSettings();
      currentSettings.removeWhere(
        (alarm) => alarm.deviceAddress == deviceAddress && alarm.type == type,
      );
      await saveAlarmSettings(currentSettings);
    } catch (e) {
      debugPrint('Error deleting alarm setting: $e');
    }
  }

  // Save saved devices
  Future<void> saveSavedDevices(List<SavedBluetoothDevice> devices) async {
    try {
      await _ensureInitialized();
      
      List<Map<String, dynamic>> jsonList = devices
          .map((device) => device.toJson())
          .toList();
      
      String jsonString = jsonEncode(jsonList);
      await _prefs!.setString(_savedDevicesKey, jsonString);
      
      debugPrint('Saved ${devices.length} devices');
    } catch (e) {
      debugPrint('Error saving devices: $e');
    }
  }

  // Get saved devices
  Future<List<SavedBluetoothDevice>> getSavedDevices() async {
    try {
      await _ensureInitialized();
      
      String? jsonString = _prefs!.getString(_savedDevicesKey);
      if (jsonString == null) return [];
      
      List<dynamic> jsonList = jsonDecode(jsonString);
      List<SavedBluetoothDevice> devices = jsonList
          .map((json) => SavedBluetoothDevice.fromJson(json))
          .toList();
      
      debugPrint('Loaded ${devices.length} saved devices');
      return devices;
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
      return [];
    }
  }

  // Save a single device
  Future<void> saveSavedDevice(SavedBluetoothDevice device) async {
    try {
      List<SavedBluetoothDevice> currentDevices = await getSavedDevices();
      
      // Update existing or add new
      int existingIndex = currentDevices.indexWhere(
        (d) => d.address == device.address,
      );
      
      if (existingIndex != -1) {
        currentDevices[existingIndex] = device;
      } else {
        currentDevices.add(device);
      }
      
      await saveSavedDevices(currentDevices);
    } catch (e) {
      debugPrint('Error saving single device: $e');
    }
  }

  // Delete saved device
  Future<void> deleteSavedDevice(String deviceAddress) async {
    try {
      List<SavedBluetoothDevice> currentDevices = await getSavedDevices();
      currentDevices.removeWhere((device) => device.address == deviceAddress);
      await saveSavedDevices(currentDevices);
    } catch (e) {
      debugPrint('Error deleting saved device: $e');
    }
  }

  // App settings
  Future<void> saveAppSetting(String key, dynamic value) async {
    try {
      await _ensureInitialized();
      
      Map<String, dynamic> currentSettings = await getAppSettings();
      currentSettings[key] = value;
      
      String jsonString = jsonEncode(currentSettings);
      await _prefs!.setString(_appSettingsKey, jsonString);
    } catch (e) {
      debugPrint('Error saving app setting: $e');
    }
  }

  Future<T?> getAppSetting<T>(String key) async {
    try {
      Map<String, dynamic> settings = await getAppSettings();
      return settings[key] as T?;
    } catch (e) {
      debugPrint('Error getting app setting: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>> getAppSettings() async {
    try {
      await _ensureInitialized();
      
      String? jsonString = _prefs!.getString(_appSettingsKey);
      if (jsonString == null) return {};
      
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      debugPrint('Error loading app settings: $e');
      return {};
    }
  }

  // Utility methods
  Future<void> clearAllData() async {
    try {
      await _ensureInitialized();
      await _prefs!.clear();
      debugPrint('All data cleared');
    } catch (e) {
      debugPrint('Error clearing data: $e');
    }
  }

  Future<void> clearAlarmSettings() async {
    try {
      await _ensureInitialized();
      await _prefs!.remove(_alarmSettingsKey);
      debugPrint('Alarm settings cleared');
    } catch (e) {
      debugPrint('Error clearing alarm settings: $e');
    }
  }

  Future<void> clearSavedDevices() async {
    try {
      await _ensureInitialized();
      await _prefs!.remove(_savedDevicesKey);
      debugPrint('Saved devices cleared');
    } catch (e) {
      debugPrint('Error clearing saved devices: $e');
    }
  }

  // Export/Import functionality
  Future<String> exportData() async {
    try {
      Map<String, dynamic> allData = {
        'alarmSettings': await getAlarmSettings(),
        'savedDevices': await getSavedDevices(),
        'appSettings': await getAppSettings(),
        'exportDate': DateTime.now().toIso8601String(),
        'version': '1.0.0',
      };
      
      return jsonEncode(allData);
    } catch (e) {
      debugPrint('Error exporting data: $e');
      return '';
    }
  }

  Future<bool> importData(String jsonData) async {
    try {
      Map<String, dynamic> data = jsonDecode(jsonData);
      
      // Validate data structure
      if (!data.containsKey('version')) {
        throw Exception('Invalid data format');
      }
      
      // Import alarm settings
      if (data.containsKey('alarmSettings')) {
        List<AlarmSettings> alarmSettings = (data['alarmSettings'] as List)
            .map((json) => AlarmSettings.fromJson(json))
            .toList();
        await saveAlarmSettings(alarmSettings);
      }
      
      // Import saved devices
      if (data.containsKey('savedDevices')) {
        List<SavedBluetoothDevice> savedDevices = (data['savedDevices'] as List)
            .map((json) => SavedBluetoothDevice.fromJson(json))
            .toList();
        await saveSavedDevices(savedDevices);
      }
      
      // Import app settings
      if (data.containsKey('appSettings')) {
        Map<String, dynamic> appSettings = data['appSettings'];
        String jsonString = jsonEncode(appSettings);
        await _prefs!.setString(_appSettingsKey, jsonString);
      }
      
      debugPrint('Data imported successfully');
      return true;
    } catch (e) {
      debugPrint('Error importing data: $e');
      return false;
    }
  }
}
