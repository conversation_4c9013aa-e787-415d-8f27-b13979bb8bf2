import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';
import '../services/bluetooth_service.dart';
import '../models/bluetooth_device_model.dart';
import '../widgets/device_card.dart';
import '../widgets/quick_action_button.dart';
import 'alarm_settings_screen.dart';

class BluetoothScanScreen extends StatefulWidget {
  const BluetoothScanScreen({super.key});

  @override
  State<BluetoothScanScreen> createState() => _BluetoothScanScreenState();
}

class _BluetoothScanScreenState extends State<BluetoothScanScreen>
    with TickerProviderStateMixin {
  late AnimationController _scanAnimationController;
  bool _isConnecting = false;
  String? _connectingDeviceAddress;

  @override
  void initState() {
    super.initState();
    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _checkBluetoothAndStartScan();
  }

  @override
  void dispose() {
    _scanAnimationController.dispose();
    super.dispose();
  }

  Future<void> _checkBluetoothAndStartScan() async {
    final bluetoothService = context.read<BluetoothService>();
    
    if (!bluetoothService.isBluetoothEnabled) {
      bool enabled = await bluetoothService.enableBluetooth();
      if (!enabled) {
        _showBluetoothDisabledDialog();
        return;
      }
    }
    
    await _startScan();
  }

  Future<void> _startScan() async {
    final bluetoothService = context.read<BluetoothService>();
    await bluetoothService.startDiscovery();
    _scanAnimationController.repeat();
  }

  Future<void> _stopScan() async {
    final bluetoothService = context.read<BluetoothService>();
    await bluetoothService.stopDiscovery();
    _scanAnimationController.stop();
  }

  void _showBluetoothDisabledDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bluetooth Disabled'),
        content: const Text(
          'Bluetooth is required to scan for devices. Please enable Bluetooth and try again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkBluetoothAndStartScan();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Devices'),
        actions: [
          Consumer<BluetoothService>(
            builder: (context, bluetoothService, child) {
              return IconButton(
                onPressed: bluetoothService.isScanning ? _stopScan : _startScan,
                icon: Icon(
                  bluetoothService.isScanning
                      ? Icons.stop
                      : Icons.refresh,
                ),
                tooltip: bluetoothService.isScanning ? 'Stop Scan' : 'Refresh',
              );
            },
          ),
        ],
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          return Column(
            children: [
              _buildScanHeader(bluetoothService),
              Expanded(
                child: _buildDeviceList(bluetoothService),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildScanHeader(BluetoothService bluetoothService) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlue.withOpacity(0.05),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.primaryBlue.withOpacity(0.1),
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              AnimatedBuilder(
                animation: _scanAnimationController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _scanAnimationController.value * 2 * 3.14159,
                    child: Container(
                      padding: const EdgeInsets.all(AppTheme.spacingM),
                      decoration: BoxDecoration(
                        color: bluetoothService.isScanning
                            ? AppTheme.primaryBlue.withOpacity(0.2)
                            : AppTheme.onSurfaceVariant.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusL),
                      ),
                      child: Icon(
                        Icons.bluetooth_searching,
                        color: bluetoothService.isScanning
                            ? AppTheme.primaryBlue
                            : AppTheme.onSurfaceVariant,
                        size: 32,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: AppTheme.spacingL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bluetoothService.isScanning
                          ? 'Scanning for devices...'
                          : 'Scan stopped',
                      style: AppTheme.headlineSmall,
                    ),
                    Text(
                      bluetoothService.isScanning
                          ? 'Found ${bluetoothService.discoveredDevices.length} device${bluetoothService.discoveredDevices.length != 1 ? 's' : ''}'
                          : 'Tap refresh to scan again',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (bluetoothService.isScanning) ...[
            const SizedBox(height: AppTheme.spacingL),
            LinearProgressIndicator(
              backgroundColor: AppTheme.primaryBlue.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryBlue),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildDeviceList(BluetoothService bluetoothService) {
    final pairedDevices = bluetoothService.pairedDevices;
    final discoveredDevices = bluetoothService.discoveredDevices
        .where((device) => !pairedDevices.any((paired) => paired.address == device.address))
        .toList();

    if (pairedDevices.isEmpty && discoveredDevices.isEmpty) {
      return _buildEmptyState(bluetoothService.isScanning);
    }

    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      children: [
        if (pairedDevices.isNotEmpty) ...[
          _buildSectionHeader('Paired Devices', pairedDevices.length),
          const SizedBox(height: AppTheme.spacingM),
          ...pairedDevices.map((device) => _buildDeviceItem(device, true)),
          const SizedBox(height: AppTheme.spacingL),
        ],
        if (discoveredDevices.isNotEmpty) ...[
          _buildSectionHeader('Available Devices', discoveredDevices.length),
          const SizedBox(height: AppTheme.spacingM),
          ...discoveredDevices.map((device) => _buildDeviceItem(device, false)),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    return Row(
      children: [
        Text(
          title,
          style: AppTheme.headlineSmall,
        ),
        const SizedBox(width: AppTheme.spacingS),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingS,
            vertical: AppTheme.spacingXS,
          ),
          decoration: BoxDecoration(
            color: AppTheme.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusS),
          ),
          child: Text(
            count.toString(),
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.primaryBlue,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceItem(BluetoothDeviceModel device, bool isPaired) {
    final isConnecting = _isConnecting && _connectingDeviceAddress == device.address;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: DeviceCard(
        device: device,
        onTap: isPaired ? () => _navigateToAlarmSettings(device) : null,
        onConnect: isConnecting ? null : () => _handleDeviceAction(device),
        onDisconnect: isConnecting ? null : () => _handleDisconnect(device),
        showActions: true,
      ),
    );
  }

  Widget _buildEmptyState(bool isScanning) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isScanning ? Icons.bluetooth_searching : Icons.bluetooth_disabled,
              size: 64,
              color: AppTheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              isScanning ? 'Searching for devices...' : 'No devices found',
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              isScanning
                  ? 'Make sure your device is discoverable and nearby'
                  : 'Tap the refresh button to scan for devices',
              style: AppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (!isScanning) ...[
              const SizedBox(height: AppTheme.spacingL),
              ActionButton(
                icon: Icons.refresh,
                label: 'Start Scanning',
                onPressed: _startScan,
              ),
            ],
          ],
        ),
      ),
    ).animate().fadeIn(delay: 300.ms, duration: 600.ms);
  }

  Future<void> _handleDeviceAction(BluetoothDeviceModel device) async {
    setState(() {
      _isConnecting = true;
      _connectingDeviceAddress = device.address;
    });

    try {
      final bluetoothService = context.read<BluetoothService>();
      bool success;

      if (device.isPaired) {
        success = await bluetoothService.connectToDevice(device);
        if (success) {
          _showSuccessSnackBar('Connected to ${device.displayName}');
        } else {
          _showErrorSnackBar('Failed to connect to ${device.displayName}');
        }
      } else {
        success = await bluetoothService.pairDevice(device);
        if (success) {
          _showSuccessSnackBar('Paired with ${device.displayName}');
          // Navigate to alarm settings after successful pairing
          _navigateToAlarmSettings(device);
        } else {
          _showErrorSnackBar('Failed to pair with ${device.displayName}');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error: ${e.toString()}');
    } finally {
      setState(() {
        _isConnecting = false;
        _connectingDeviceAddress = null;
      });
    }
  }

  Future<void> _handleDisconnect(BluetoothDeviceModel device) async {
    final bluetoothService = context.read<BluetoothService>();
    await bluetoothService.disconnectDevice();
    _showSuccessSnackBar('Disconnected from ${device.displayName}');
  }

  void _navigateToAlarmSettings(BluetoothDeviceModel device) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AlarmSettingsScreen(device: device),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
