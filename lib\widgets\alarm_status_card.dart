import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../models/alarm_settings_model.dart';
import '../services/alarm_service.dart';

class AlarmStatusCard extends StatelessWidget {
  final ActiveAlarm alarm;

  const AlarmStatusCard({
    super.key,
    required this.alarm,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
          gradient: LinearGradient(
            colors: [
              AppTheme.error.withOpacity(0.1),
              AppTheme.error.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: AppTheme.spacingM),
              _buildAlarmInfo(),
              const SizedBox(height: AppTheme.spacingM),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 400.ms).slideX(begin: 0.2);
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingS),
          decoration: BoxDecoration(
            color: AppTheme.error.withOpacity(0.2),
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
          ),
          child: Icon(
            _getAlarmIcon(),
            color: AppTheme.error,
            size: 24,
          ),
        ).animate(onPlay: (controller) => controller.repeat())
            .shake(duration: 1000.ms, hz: 2),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ALARM ACTIVE',
                style: AppTheme.labelLarge.copyWith(
                  color: AppTheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                alarm.settings.typeDisplayName,
                style: AppTheme.bodyMedium,
              ),
            ],
          ),
        ),
        _buildDurationBadge(),
      ],
    );
  }

  Widget _buildAlarmInfo() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bluetooth,
                size: 20,
                color: AppTheme.primaryBlue,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Text(
                  alarm.settings.deviceName,
                  style: AppTheme.labelLarge,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingS),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: AppTheme.onSurfaceVariant,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'Triggered at ${_formatTime(alarm.triggeredAt)}',
                style: AppTheme.bodySmall,
              ),
            ],
          ),
          if (alarm.snoozeCount > 0) ...[
            const SizedBox(height: AppTheme.spacingS),
            Row(
              children: [
                Icon(
                  Icons.snooze,
                  size: 16,
                  color: AppTheme.warning,
                ),
                const SizedBox(width: AppTheme.spacingS),
                Text(
                  'Snoozed ${alarm.snoozeCount} time${alarm.snoozeCount > 1 ? 's' : ''}',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.warning,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final alarmService = context.read<AlarmService>();

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => alarmService.stopAlarm(alarm.id),
            icon: const Icon(Icons.stop, size: 18),
            label: const Text('Stop'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.error,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
            ),
          ),
        ),
        if (alarm.canSnooze) ...[
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => alarmService.snoozeAlarm(alarm.id),
              icon: const Icon(Icons.snooze, size: 18),
              label: Text('Snooze (${alarm.settings.snoozeDuration}m)'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.warning,
                side: BorderSide(color: AppTheme.warning),
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDurationBadge() {
    final duration = alarm.activeDuration;
    String durationText;

    if (duration.inHours > 0) {
      durationText = '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      durationText = '${duration.inMinutes}m';
    } else {
      durationText = '${duration.inSeconds}s';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: AppTheme.error,
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        durationText,
        style: AppTheme.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  IconData _getAlarmIcon() {
    switch (alarm.settings.type) {
      case AlarmType.disconnect:
        return Icons.bluetooth_disabled;
      case AlarmType.connect:
        return Icons.bluetooth_connected;
      case AlarmType.proximity:
        return Icons.near_me;
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}

class AlarmSummaryCard extends StatelessWidget {
  final List<AlarmSettings> alarmSettings;
  final VoidCallback? onTap;

  const AlarmSummaryCard({
    super.key,
    required this.alarmSettings,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final enabledAlarms = alarmSettings.where((alarm) => alarm.isEnabled).length;
    final totalAlarms = alarmSettings.length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingS),
                    decoration: BoxDecoration(
                      color: enabledAlarms > 0
                          ? AppTheme.success.withOpacity(0.1)
                          : AppTheme.onSurfaceVariant.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: Icon(
                      enabledAlarms > 0 ? Icons.alarm_on : Icons.alarm_off,
                      color: enabledAlarms > 0
                          ? AppTheme.success
                          : AppTheme.onSurfaceVariant,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Alarm Settings',
                          style: AppTheme.labelLarge,
                        ),
                        Text(
                          '$enabledAlarms of $totalAlarms alarms enabled',
                          style: AppTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: AppTheme.onSurfaceVariant,
                  ),
                ],
              ),
              if (totalAlarms > 0) ...[
                const SizedBox(height: AppTheme.spacingM),
                _buildAlarmTypesSummary(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlarmTypesSummary() {
    final disconnectAlarms = alarmSettings
        .where((alarm) => alarm.type == AlarmType.disconnect && alarm.isEnabled)
        .length;
    final connectAlarms = alarmSettings
        .where((alarm) => alarm.type == AlarmType.connect && alarm.isEnabled)
        .length;
    final proximityAlarms = alarmSettings
        .where((alarm) => alarm.type == AlarmType.proximity && alarm.isEnabled)
        .length;

    return Row(
      children: [
        if (disconnectAlarms > 0)
          _buildAlarmTypeBadge(
            Icons.bluetooth_disabled,
            disconnectAlarms.toString(),
            AppTheme.error,
          ),
        if (connectAlarms > 0) ...[
          if (disconnectAlarms > 0) const SizedBox(width: AppTheme.spacingS),
          _buildAlarmTypeBadge(
            Icons.bluetooth_connected,
            connectAlarms.toString(),
            AppTheme.success,
          ),
        ],
        if (proximityAlarms > 0) ...[
          if (disconnectAlarms > 0 || connectAlarms > 0)
            const SizedBox(width: AppTheme.spacingS),
          _buildAlarmTypeBadge(
            Icons.near_me,
            proximityAlarms.toString(),
            AppTheme.warning,
          ),
        ],
      ],
    );
  }

  Widget _buildAlarmTypeBadge(IconData icon, String count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: AppTheme.spacingXS),
          Text(
            count,
            style: AppTheme.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
