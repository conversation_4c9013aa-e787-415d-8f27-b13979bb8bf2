import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';

class BluetoothDeviceModel {
  final String name;
  final String address;
  final bool isConnected;
  final bool isPaired;
  final int? rssi;
  final DateTime? lastSeen;
  final BluetoothDeviceType type;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    this.isConnected = false,
    this.isPaired = false,
    this.rssi,
    this.lastSeen,
    this.type = BluetoothDeviceType.unknown,
  });

  factory BluetoothDeviceModel.fromBluetoothDevice(BluetoothDevice device) {
    return BluetoothDeviceModel(
      name: device.name ?? 'Unknown Device',
      address: device.address,
      isPaired: device.isBonded,
      isConnected: device.isConnected,
      type: device.type,
    );
  }

  factory BluetoothDeviceModel.fromDiscoveryResult(
      BluetoothDiscoveryResult result) {
    return BluetoothDeviceModel(
      name: result.device.name ?? 'Unknown Device',
      address: result.device.address,
      isPaired: result.device.isBonded,
      isConnected: result.device.isConnected,
      rssi: result.rssi,
      type: result.device.type,
      lastSeen: DateTime.now(),
    );
  }

  factory BluetoothDeviceModel.fromJson(Map<String, dynamic> json) {
    return BluetoothDeviceModel(
      name: json['name'] ?? 'Unknown Device',
      address: json['address'],
      isConnected: json['isConnected'] ?? false,
      isPaired: json['isPaired'] ?? false,
      rssi: json['rssi'],
      lastSeen:
          json['lastSeen'] != null ? DateTime.parse(json['lastSeen']) : null,
      type: _parseBluetoothDeviceType(json['type'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'isConnected': isConnected,
      'isPaired': isPaired,
      'rssi': rssi,
      'lastSeen': lastSeen?.toIso8601String(),
      'type': _serializeBluetoothDeviceType(type),
    };
  }

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    bool? isConnected,
    bool? isPaired,
    int? rssi,
    DateTime? lastSeen,
    BluetoothDeviceType? type,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      isConnected: isConnected ?? this.isConnected,
      isPaired: isPaired ?? this.isPaired,
      rssi: rssi ?? this.rssi,
      lastSeen: lastSeen ?? this.lastSeen,
      type: type ?? this.type,
    );
  }

  String get displayName {
    if (name.isNotEmpty && name != 'Unknown Device') {
      return name;
    }
    return 'Device ${address.substring(address.length - 5)}';
  }

  String get connectionStatus {
    if (isConnected) return 'Connected';
    if (isPaired) return 'Paired';
    return 'Available';
  }

  String get signalStrength {
    if (rssi == null) return 'Unknown';
    if (rssi! > -50) return 'Excellent';
    if (rssi! > -60) return 'Good';
    if (rssi! > -70) return 'Fair';
    return 'Poor';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluetoothDeviceModel && other.address == address;
  }

  @override
  int get hashCode => address.hashCode;

  @override
  String toString() {
    return 'BluetoothDeviceModel(name: $name, address: $address, isConnected: $isConnected, isPaired: $isPaired)';
  }

  // Helper methods for BluetoothDeviceType serialization
  static BluetoothDeviceType _parseBluetoothDeviceType(int value) {
    switch (value) {
      case 1:
        return BluetoothDeviceType.classic;
      case 2:
        return BluetoothDeviceType.le;
      case 3:
        return BluetoothDeviceType.dual;
      default:
        return BluetoothDeviceType.unknown;
    }
  }

  static int _serializeBluetoothDeviceType(BluetoothDeviceType type) {
    switch (type) {
      case BluetoothDeviceType.classic:
        return 1;
      case BluetoothDeviceType.le:
        return 2;
      case BluetoothDeviceType.dual:
        return 3;
      case BluetoothDeviceType.unknown:
      default:
        return 0;
    }
  }
}

enum DeviceConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
}

class SavedBluetoothDevice {
  final String address;
  final String name;
  final bool alarmOnDisconnect;
  final bool alarmOnConnect;
  final DateTime savedAt;
  final String? customName;

  SavedBluetoothDevice({
    required this.address,
    required this.name,
    this.alarmOnDisconnect = true,
    this.alarmOnConnect = false,
    required this.savedAt,
    this.customName,
  });

  factory SavedBluetoothDevice.fromJson(Map<String, dynamic> json) {
    return SavedBluetoothDevice(
      address: json['address'],
      name: json['name'],
      alarmOnDisconnect: json['alarmOnDisconnect'] ?? true,
      alarmOnConnect: json['alarmOnConnect'] ?? false,
      savedAt: DateTime.parse(json['savedAt']),
      customName: json['customName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'name': name,
      'alarmOnDisconnect': alarmOnDisconnect,
      'alarmOnConnect': alarmOnConnect,
      'savedAt': savedAt.toIso8601String(),
      'customName': customName,
    };
  }

  String get displayName => customName ?? name;

  SavedBluetoothDevice copyWith({
    String? address,
    String? name,
    bool? alarmOnDisconnect,
    bool? alarmOnConnect,
    DateTime? savedAt,
    String? customName,
  }) {
    return SavedBluetoothDevice(
      address: address ?? this.address,
      name: name ?? this.name,
      alarmOnDisconnect: alarmOnDisconnect ?? this.alarmOnDisconnect,
      alarmOnConnect: alarmOnConnect ?? this.alarmOnConnect,
      savedAt: savedAt ?? this.savedAt,
      customName: customName ?? this.customName,
    );
  }
}
