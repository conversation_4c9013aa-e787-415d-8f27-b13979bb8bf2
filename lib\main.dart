import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'theme/app_theme.dart';
import 'services/bluetooth_service.dart';
import 'services/alarm_service.dart';
import 'services/storage_service.dart';
import 'screens/home_screen.dart';

final GetIt getIt = GetIt.instance;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await _initializeServices();

  runApp(const BluetoothAlarmApp());
}

Future<void> _initializeServices() async {
  // Register services
  getIt.registerSingleton<StorageService>(StorageService());
  getIt.registerSingleton<BluetoothService>(BluetoothService());
  getIt.registerSingleton<AlarmService>(AlarmService());

  // Initialize services
  await getIt<StorageService>().initialize();
  await getIt<BluetoothService>().initialize();
  await getIt<AlarmService>().initialize();
}

class BluetoothAlarmApp extends StatelessWidget {
  const BluetoothAlarmApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<BluetoothService>.value(
          value: getIt<BluetoothService>(),
        ),
        ChangeNotifierProvider<AlarmService>.value(
          value: getIt<AlarmService>(),
        ),
      ],
      child: MaterialApp(
        title: 'Bluetooth Alarm',
        theme: AppTheme.lightTheme,
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
