// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:flutter_application_2/main.dart';

void main() {
  group('Bluetooth Alarm App Tests', () {
    testWidgets('App loads and shows home screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const BluetoothAlarmApp());
      await tester.pumpAndSettle();

      // Verify that the app loads with the home screen
      expect(find.text('Bluetooth Alarm'), findsOneWidget);
      expect(find.text('Quick Actions'), findsOneWidget);
      expect(find.text('My Devices'), findsOneWidget);
      expect(find.text('Alarm Status'), findsOneWidget);
    });

    testWidgets('Navigation to scan screen works', (WidgetTester tester) async {
      await tester.pumpWidget(const BluetoothAlarmApp());
      await tester.pumpAndSettle();

      // Find and tap the scan devices button
      final scanButton = find.text('Scan Devices');
      expect(scanButton, findsOneWidget);

      await tester.tap(scanButton);
      await tester.pumpAndSettle();

      // Verify we're on the scan screen
      expect(find.text('Scan Devices'), findsOneWidget);
      expect(find.text('Scanning for devices...'), findsAny);
    });

    testWidgets('Quick actions are displayed', (WidgetTester tester) async {
      await tester.pumpWidget(const BluetoothAlarmApp());
      await tester.pumpAndSettle();

      // Check for quick action buttons
      expect(find.text('Enable Bluetooth'), findsAny);
      expect(find.text('Start Monitoring'), findsAny);
    });

    testWidgets('Empty state is shown when no devices',
        (WidgetTester tester) async {
      await tester.pumpWidget(const BluetoothAlarmApp());
      await tester.pumpAndSettle();

      // Should show empty state for devices
      expect(find.text('No Devices Found'), findsAny);
      expect(find.text('No Active Alarms'), findsAny);
    });
  });
}
