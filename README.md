# flutter_application_2

A new Flutter project.

## Getting Started

# Bluetooth Alarm Android App

A modern Flutter application that monitors Bluetooth devices and triggers customizable alarms based on device connection status.

## Features

### Core Functionality

- **Bluetooth Device Scanning**: Discover and scan for nearby Bluetooth devices
- **Device Pairing**: Pair with Bluetooth devices for monitoring
- **Smart Alarms**: Set up alarms for different scenarios:
  - **Disconnect Alarm**: Triggers when a paired device disconnects or goes out of range
  - **Connect Alarm**: Triggers when a device connects or comes into range
  - **Proximity Alarm**: Triggers based on device proximity (RSSI)

### Alarm Customization

- **Sound Options**: Choose from multiple alarm sounds (default, beep, siren, bell, custom)
- **Volume Control**: Adjustable alarm volume
- **Vibration Patterns**: Multiple vibration patterns (short, long, custom pattern)
- **Snooze Functionality**: Configurable snooze duration and maximum snooze count
- **Auto-Stop**: Automatically stop alarms after a specified duration

### Modern UI/UX

- **Material 3 Design**: Clean, modern interface following Material Design guidelines
- **Smooth Animations**: Fluid animations using flutter_animate
- **Responsive Layout**: Optimized for different screen sizes
- **Dark/Light Theme Support**: Consistent theming throughout the app
- **Custom Components**: Reusable UI components for consistent design

## Architecture

### Services

- **BluetoothService**: Manages Bluetooth operations (scanning, pairing, connection monitoring)
- **AlarmService**: Handles alarm logic, monitoring, and notifications
- **StorageService**: Manages local data persistence using SharedPreferences

### Models

- **BluetoothDeviceModel**: Represents Bluetooth device information
- **AlarmSettings**: Configuration for different alarm types
- **ActiveAlarm**: Represents currently active alarms

### State Management

- **Provider**: Used for state management across the app
- **GetIt**: Service locator for dependency injection

## Setup Instructions

### Prerequisites

- Flutter SDK (3.6.0 or higher)
- Android Studio or VS Code with Flutter extensions
- Android device or emulator with Bluetooth support

### Installation

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Connect an Android device or start an emulator
4. Run `flutter run` to launch the app

### Permissions

The app requires the following permissions:

- Bluetooth and Bluetooth Admin
- Location (required for Bluetooth scanning on Android)
- Vibration
- Notifications

## Usage

### Getting Started

1. **Enable Bluetooth**: Ensure Bluetooth is enabled on your device
2. **Grant Permissions**: Allow the app to access Bluetooth and location
3. **Scan for Devices**: Tap the "Scan Devices" button to discover nearby Bluetooth devices
4. **Pair Devices**: Select a device to pair and set up alarms

### Setting Up Alarms

1. **Select Device**: Choose a paired Bluetooth device
2. **Configure Alarm Types**: Enable/disable different alarm types (disconnect, connect, proximity)
3. **Customize Settings**: Adjust sound, volume, vibration, snooze, and auto-stop settings
4. **Save Settings**: Save your configuration

### Monitoring

- The app continuously monitors paired devices when alarms are enabled
- Active alarms are displayed on the home screen
- Use quick actions to start/stop monitoring or manage Bluetooth

## Technical Details

### Dependencies

- `flutter_bluetooth_serial`: Bluetooth functionality
- `permission_handler`: Runtime permissions
- `audioplayers`: Audio playback for alarms
- `vibration`: Device vibration control
- `flutter_local_notifications`: Local notifications
- `provider`: State management
- `shared_preferences`: Local data storage
- `google_fonts`: Custom typography
- `flutter_animate`: Smooth animations

### File Structure

```
lib/
├── main.dart                 # App entry point
├── theme/
│   └── app_theme.dart       # Global theme and styling
├── models/                  # Data models
├── services/                # Business logic services
├── screens/                 # UI screens
└── widgets/                 # Reusable UI components
```

## Known Issues

- Some deprecation warnings for `withOpacity` (will be fixed in future updates)
- Bluetooth functionality requires physical Android device (emulator limitations)
- Custom sound files need to be added to assets/sounds/ directory

## Future Enhancements

- iOS support
- Bluetooth Low Energy (BLE) support
- Custom sound file picker
- Export/import alarm settings
- Multiple device monitoring
- Geofencing integration
- Widget support

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is licensed under the MIT License.
