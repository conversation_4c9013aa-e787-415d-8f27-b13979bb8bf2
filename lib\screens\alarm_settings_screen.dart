import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';
import '../models/bluetooth_device_model.dart';
import '../models/alarm_settings_model.dart';
import '../services/alarm_service.dart';
import '../widgets/quick_action_button.dart';

class AlarmSettingsScreen extends StatefulWidget {
  final BluetoothDeviceModel device;

  const AlarmSettingsScreen({
    super.key,
    required this.device,
  });

  @override
  State<AlarmSettingsScreen> createState() => _AlarmSettingsScreenState();
}

class _AlarmSettingsScreenState extends State<AlarmSettingsScreen> {
  late List<AlarmSettings> _alarmSettings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAlarmSettings();
  }

  Future<void> _loadAlarmSettings() async {
    final alarmService = context.read<AlarmService>();
    final allSettings = alarmService.alarmSettings;

    _alarmSettings = allSettings
        .where((setting) => setting.deviceAddress == widget.device.address)
        .toList();

    // Create default settings if none exist
    if (_alarmSettings.isEmpty) {
      _alarmSettings = [
        AlarmSettings(
          type: AlarmType.disconnect,
          deviceAddress: widget.device.address,
          deviceName: widget.device.displayName,
          createdAt: DateTime.now(),
        ),
        AlarmSettings(
          type: AlarmType.connect,
          deviceAddress: widget.device.address,
          deviceName: widget.device.displayName,
          createdAt: DateTime.now(),
          isEnabled: false,
        ),
      ];
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Alarm Settings'),
            Text(
              widget.device.displayName,
              style: AppTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _saveAllSettings,
            icon: const Icon(Icons.save),
            tooltip: 'Save Settings',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDeviceInfo(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildAlarmTypesList(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildQuickActions(),
                ],
              ),
            ),
    );
  }

  Widget _buildDeviceInfo() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: AppTheme.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusL),
                ),
                child: Icon(
                  Icons.bluetooth,
                  color: AppTheme.primaryBlue,
                  size: 32,
                ),
              ),
              const SizedBox(width: AppTheme.spacingL),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.device.displayName,
                      style: AppTheme.headlineSmall,
                    ),
                    Text(
                      widget.device.address,
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingS),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: widget.device.isConnected
                            ? AppTheme.success.withOpacity(0.1)
                            : AppTheme.onSurfaceVariant.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusS),
                      ),
                      child: Text(
                        widget.device.connectionStatus,
                        style: AppTheme.bodySmall.copyWith(
                          color: widget.device.isConnected
                              ? AppTheme.success
                              : AppTheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.1);
  }

  Widget _buildAlarmTypesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Alarm Types',
          style: AppTheme.headlineSmall,
        ),
        const SizedBox(height: AppTheme.spacingM),
        ..._alarmSettings.map((setting) => _buildAlarmSettingCard(setting)),
      ],
    );
  }

  Widget _buildAlarmSettingCard(AlarmSettings setting) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      decoration: AppTheme.cardDecoration,
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(AppTheme.spacingS),
          decoration: BoxDecoration(
            color: _getAlarmTypeColor(setting.type).withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
          ),
          child: Icon(
            _getAlarmTypeIcon(setting.type),
            color: _getAlarmTypeColor(setting.type),
          ),
        ),
        title: Text(
          setting.typeDisplayName,
          style: AppTheme.labelLarge,
        ),
        subtitle: Text(
          _getAlarmTypeDescription(setting.type),
          style: AppTheme.bodySmall,
        ),
        trailing: Switch(
          value: setting.isEnabled,
          onChanged: (value) => _updateAlarmSetting(
            setting.copyWith(isEnabled: value),
          ),
          activeColor: AppTheme.primaryBlue,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: _buildAlarmSettingsForm(setting),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 200.ms, duration: 600.ms).slideX(begin: 0.1);
  }

  Widget _buildAlarmSettingsForm(AlarmSettings setting) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSoundSettings(setting),
        const SizedBox(height: AppTheme.spacingL),
        _buildVibrationSettings(setting),
        const SizedBox(height: AppTheme.spacingL),
        _buildSnoozeSettings(setting),
        const SizedBox(height: AppTheme.spacingL),
        _buildAutoStopSettings(setting),
      ],
    );
  }

  Widget _buildSoundSettings(AlarmSettings setting) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sound Settings',
          style: AppTheme.labelLarge,
        ),
        const SizedBox(height: AppTheme.spacingM),
        DropdownButtonFormField<AlarmSound>(
          value: setting.sound,
          decoration: const InputDecoration(
            labelText: 'Alarm Sound',
            prefixIcon: Icon(Icons.volume_up),
          ),
          items: AlarmSound.values.map((sound) {
            return DropdownMenuItem(
              value: sound,
              child: Text(setting.copyWith(sound: sound).soundDisplayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateAlarmSetting(setting.copyWith(sound: value));
            }
          },
        ),
        const SizedBox(height: AppTheme.spacingM),
        Text(
          'Volume: ${(setting.volume * 100).round()}%',
          style: AppTheme.bodyMedium,
        ),
        Slider(
          value: setting.volume,
          onChanged: (value) {
            _updateAlarmSetting(setting.copyWith(volume: value));
          },
          activeColor: AppTheme.primaryBlue,
        ),
      ],
    );
  }

  Widget _buildVibrationSettings(AlarmSettings setting) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Vibration',
              style: AppTheme.labelLarge,
            ),
            Switch(
              value: setting.vibrate,
              onChanged: (value) {
                _updateAlarmSetting(setting.copyWith(vibrate: value));
              },
              activeColor: AppTheme.primaryBlue,
            ),
          ],
        ),
        if (setting.vibrate) ...[
          const SizedBox(height: AppTheme.spacingM),
          DropdownButtonFormField<int>(
            value: setting.vibrationPattern,
            decoration: const InputDecoration(
              labelText: 'Vibration Pattern',
              prefixIcon: Icon(Icons.vibration),
            ),
            items: const [
              DropdownMenuItem(value: 0, child: Text('Short')),
              DropdownMenuItem(value: 1, child: Text('Long')),
              DropdownMenuItem(value: 2, child: Text('Pattern')),
            ],
            onChanged: (value) {
              if (value != null) {
                _updateAlarmSetting(setting.copyWith(vibrationPattern: value));
              }
            },
          ),
        ],
      ],
    );
  }

  Widget _buildSnoozeSettings(AlarmSettings setting) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Snooze',
              style: AppTheme.labelLarge,
            ),
            Switch(
              value: setting.snoozeEnabled,
              onChanged: (value) {
                _updateAlarmSetting(setting.copyWith(snoozeEnabled: value));
              },
              activeColor: AppTheme.primaryBlue,
            ),
          ],
        ),
        if (setting.snoozeEnabled) ...[
          const SizedBox(height: AppTheme.spacingM),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: setting.snoozeDuration.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Snooze Duration (minutes)',
                    prefixIcon: Icon(Icons.snooze),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final duration =
                        int.tryParse(value) ?? setting.snoozeDuration;
                    _updateAlarmSetting(
                        setting.copyWith(snoozeDuration: duration));
                  },
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: TextFormField(
                  initialValue: setting.maxSnoozeCount.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Max Snoozes',
                    prefixIcon: Icon(Icons.repeat),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final count = int.tryParse(value) ?? setting.maxSnoozeCount;
                    _updateAlarmSetting(
                        setting.copyWith(maxSnoozeCount: count));
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildAutoStopSettings(AlarmSettings setting) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Auto Stop',
              style: AppTheme.labelLarge,
            ),
            Switch(
              value: setting.autoStopEnabled,
              onChanged: (value) {
                _updateAlarmSetting(setting.copyWith(autoStopEnabled: value));
              },
              activeColor: AppTheme.primaryBlue,
            ),
          ],
        ),
        if (setting.autoStopEnabled) ...[
          const SizedBox(height: AppTheme.spacingM),
          TextFormField(
            initialValue: setting.autoStopDuration.toString(),
            decoration: const InputDecoration(
              labelText: 'Auto Stop After (minutes)',
              prefixIcon: Icon(Icons.timer_off),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final duration = int.tryParse(value) ?? setting.autoStopDuration;
              _updateAlarmSetting(setting.copyWith(autoStopDuration: duration));
            },
          ),
        ],
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headlineSmall,
        ),
        const SizedBox(height: AppTheme.spacingM),
        Row(
          children: [
            Expanded(
              child: QuickActionButton(
                icon: Icons.play_arrow,
                label: 'Test Alarm',
                onTap: _testAlarm,
                color: AppTheme.warning,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: QuickActionButton(
                icon: Icons.delete,
                label: 'Reset Settings',
                onTap: _resetSettings,
                color: AppTheme.error,
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getAlarmTypeIcon(AlarmType type) {
    switch (type) {
      case AlarmType.disconnect:
        return Icons.bluetooth_disabled;
      case AlarmType.connect:
        return Icons.bluetooth_connected;
      case AlarmType.proximity:
        return Icons.near_me;
    }
  }

  Color _getAlarmTypeColor(AlarmType type) {
    switch (type) {
      case AlarmType.disconnect:
        return AppTheme.error;
      case AlarmType.connect:
        return AppTheme.success;
      case AlarmType.proximity:
        return AppTheme.warning;
    }
  }

  String _getAlarmTypeDescription(AlarmType type) {
    switch (type) {
      case AlarmType.disconnect:
        return 'Alarm when device disconnects or goes out of range';
      case AlarmType.connect:
        return 'Alarm when device connects or comes into range';
      case AlarmType.proximity:
        return 'Alarm based on device proximity';
    }
  }

  void _updateAlarmSetting(AlarmSettings updatedSetting) {
    setState(() {
      final index = _alarmSettings.indexWhere(
        (setting) => setting.type == updatedSetting.type,
      );
      if (index != -1) {
        _alarmSettings[index] = updatedSetting;
      }
    });
  }

  Future<void> _saveAllSettings() async {
    final alarmService = context.read<AlarmService>();

    for (final setting in _alarmSettings) {
      await alarmService.saveAlarmSettings(setting);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Alarm settings saved successfully'),
          backgroundColor: AppTheme.success,
        ),
      );
    }
  }

  void _testAlarm() {
    // Implement test alarm functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test alarm triggered'),
        backgroundColor: AppTheme.warning,
      ),
    );
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all alarm settings to default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadAlarmSettings();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.error),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
