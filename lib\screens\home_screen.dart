import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';
import '../services/bluetooth_service.dart';
import '../services/alarm_service.dart';
import '../models/bluetooth_device_model.dart';
import '../models/alarm_settings_model.dart';
import '../widgets/device_card.dart';
import '../widgets/alarm_status_card.dart';
import '../widgets/quick_action_button.dart';
import 'bluetooth_scan_screen.dart';
import 'alarm_settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animationController.forward();
    _checkPermissions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkPermissions() async {
    final bluetoothService = context.read<BluetoothService>();
    bool hasPermissions = await bluetoothService.requestPermissions();

    if (!hasPermissions) {
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Permissions Required'),
        content: const Text(
          'This app needs Bluetooth and location permissions to function properly. '
          'Please grant the required permissions in the app settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryBlue.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildQuickActions(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildAlarmStatus(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildPairedDevices(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildRecentActivity(),
                ],
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToScanScreen,
        icon: const Icon(Icons.bluetooth_searching),
        label: const Text('Scan Devices'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: Colors.white,
      ).animate().scale(delay: 800.ms, duration: 600.ms),
    );
  }

  Widget _buildHeader() {
    return Consumer<BluetoothService>(
      builder: (context, bluetoothService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.bluetooth,
                    color: AppTheme.primaryBlue,
                    size: 32,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bluetooth Alarm',
                        style: AppTheme.headlineMedium,
                      ),
                      Text(
                        bluetoothService.isBluetoothEnabled
                            ? 'Bluetooth is enabled'
                            : 'Bluetooth is disabled',
                        style: AppTheme.bodyMedium.copyWith(
                          color: bluetoothService.isBluetoothEnabled
                              ? AppTheme.success
                              : AppTheme.error,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _showSettingsMenu,
                  icon: const Icon(Icons.more_vert),
                ),
              ],
            ),
          ],
        );
      },
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2);
  }

  Widget _buildQuickActions() {
    return Consumer2<BluetoothService, AlarmService>(
      builder: (context, bluetoothService, alarmService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Row(
              children: [
                Expanded(
                  child: QuickActionButton(
                    icon: bluetoothService.isBluetoothEnabled
                        ? Icons.bluetooth_disabled
                        : Icons.bluetooth,
                    label: bluetoothService.isBluetoothEnabled
                        ? 'Disable Bluetooth'
                        : 'Enable Bluetooth',
                    onTap: _toggleBluetooth,
                    color: bluetoothService.isBluetoothEnabled
                        ? AppTheme.error
                        : AppTheme.success,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: QuickActionButton(
                    icon: alarmService.isMonitoring
                        ? Icons.pause_circle
                        : Icons.play_circle,
                    label: alarmService.isMonitoring
                        ? 'Stop Monitoring'
                        : 'Start Monitoring',
                    onTap: _toggleMonitoring,
                    color: alarmService.isMonitoring
                        ? AppTheme.warning
                        : AppTheme.success,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    ).animate().fadeIn(delay: 200.ms, duration: 600.ms).slideY(begin: 0.2);
  }

  Widget _buildAlarmStatus() {
    return Consumer<AlarmService>(
      builder: (context, alarmService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Alarm Status',
                  style: AppTheme.headlineSmall,
                ),
                if (alarmService.hasActiveAlarms)
                  TextButton(
                    onPressed: () => alarmService.stopAllAlarms(),
                    child: const Text('Stop All'),
                  ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),
            if (alarmService.hasActiveAlarms)
              ...alarmService.activeAlarms.map(
                (alarm) => Padding(
                  padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
                  child: AlarmStatusCard(alarm: alarm),
                ),
              )
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.spacingL),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 48,
                      color: AppTheme.success,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    Text(
                      'No Active Alarms',
                      style: AppTheme.headlineSmall.copyWith(
                        color: AppTheme.success,
                      ),
                    ),
                    Text(
                      'All your devices are being monitored',
                      style: AppTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    ).animate().fadeIn(delay: 400.ms, duration: 600.ms).slideY(begin: 0.2);
  }

  Widget _buildPairedDevices() {
    return Consumer<BluetoothService>(
      builder: (context, bluetoothService, child) {
        final savedDevices = bluetoothService.savedDevices;
        final pairedDevices = bluetoothService.pairedDevices;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'My Devices',
                  style: AppTheme.headlineSmall,
                ),
                TextButton.icon(
                  onPressed: _navigateToScanScreen,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Device'),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),

            // Show saved devices first
            if (savedDevices.isNotEmpty) ...[
              Text(
                'Saved Devices (${savedDevices.length})',
                style: AppTheme.labelLarge.copyWith(
                  color: AppTheme.primaryBlue,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              ...savedDevices.map(
                (savedDevice) {
                  // Find corresponding paired device
                  BluetoothDeviceModel? pairedDevice = pairedDevices
                      .where((device) => device.address == savedDevice.address)
                      .firstOrNull;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
                    child: _buildSavedDeviceCard(savedDevice, pairedDevice),
                  );
                },
              ),
              const SizedBox(height: AppTheme.spacingM),
            ],

            // Show other paired devices that aren't saved
            ...() {
              final unsavedPairedDevices = pairedDevices
                  .where((device) => !savedDevices
                      .any((saved) => saved.address == device.address))
                  .toList();

              if (unsavedPairedDevices.isNotEmpty) {
                return [
                  Text(
                    'Other Paired Devices (${unsavedPairedDevices.length})',
                    style: AppTheme.labelLarge.copyWith(
                      color: AppTheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingS),
                  ...unsavedPairedDevices.map(
                    (device) => Padding(
                      padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
                      child: DeviceCard(
                        device: device,
                        onTap: () => _navigateToAlarmSettings(device),
                      ),
                    ),
                  ),
                ];
              }
              return <Widget>[];
            }(),

            // Empty state
            if (savedDevices.isEmpty && pairedDevices.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.spacingL),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  children: [
                    Icon(
                      Icons.bluetooth_disabled,
                      size: 48,
                      color: AppTheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    Text(
                      'No Devices Found',
                      style: AppTheme.headlineSmall,
                    ),
                    Text(
                      'Tap "Add Device" to scan and pair with Bluetooth devices',
                      style: AppTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    ).animate().fadeIn(delay: 600.ms, duration: 600.ms).slideY(begin: 0.2);
  }

  Widget _buildRecentActivity() {
    return Consumer<AlarmService>(
      builder: (context, alarmService, child) {
        List<AlarmSettings> recentAlarms = alarmService.alarmSettings
            .where((alarm) => alarm.lastTriggered != null)
            .toList()
          ..sort((a, b) => b.lastTriggered!.compareTo(a.lastTriggered!));

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: AppTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingM),
            if (recentAlarms.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.spacingL),
                decoration: AppTheme.cardDecoration,
                child: Text(
                  'No recent alarm activity',
                  style: AppTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              )
            else
              ...recentAlarms.take(3).map(
                    (alarm) => Container(
                      margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
                      padding: const EdgeInsets.all(AppTheme.spacingM),
                      decoration: AppTheme.cardDecoration,
                      child: Row(
                        children: [
                          Icon(
                            _getAlarmIcon(alarm.type),
                            color: AppTheme.primaryBlue,
                          ),
                          const SizedBox(width: AppTheme.spacingM),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  alarm.typeDisplayName,
                                  style: AppTheme.labelLarge,
                                ),
                                Text(
                                  alarm.deviceName,
                                  style: AppTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          Text(
                            _formatTime(alarm.lastTriggered!),
                            style: AppTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ),
          ],
        );
      },
    ).animate().fadeIn(delay: 800.ms, duration: 600.ms).slideY(begin: 0.2);
  }

  Widget _buildSavedDeviceCard(
      SavedBluetoothDevice savedDevice, BluetoothDeviceModel? pairedDevice) {
    final bluetoothService = context.read<BluetoothService>();
    final alarmService = context.read<AlarmService>();

    // Check if device has active alarms
    final deviceAlarms = alarmService.alarmSettings
        .where((alarm) =>
            alarm.deviceAddress == savedDevice.address && alarm.isEnabled)
        .toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
      ),
      child: InkWell(
        onTap: () {
          if (pairedDevice != null) {
            _navigateToAlarmSettings(pairedDevice);
          }
        },
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingS),
                    decoration: BoxDecoration(
                      color: pairedDevice?.isConnected == true
                          ? AppTheme.success.withOpacity(0.1)
                          : AppTheme.onSurfaceVariant.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: Icon(
                      pairedDevice?.isConnected == true
                          ? Icons.bluetooth_connected
                          : Icons.bluetooth,
                      color: pairedDevice?.isConnected == true
                          ? AppTheme.success
                          : AppTheme.onSurfaceVariant,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          savedDevice.displayName,
                          style: AppTheme.labelLarge,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          savedDevice.address,
                          style: AppTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  // Alarm status indicator
                  if (deviceAlarms.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.success.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusS),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.alarm_on,
                            size: 14,
                            color: AppTheme.success,
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            '${deviceAlarms.length}',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.success,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(width: AppTheme.spacingS),
                  PopupMenuButton<String>(
                    onSelected: (value) async {
                      switch (value) {
                        case 'remove':
                          await bluetoothService
                              .removeSavedDevice(savedDevice.address);
                          break;
                        case 'settings':
                          if (pairedDevice != null) {
                            _navigateToAlarmSettings(pairedDevice);
                          }
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'settings',
                        child: Row(
                          children: [
                            Icon(Icons.settings),
                            SizedBox(width: 8),
                            Text('Alarm Settings'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'remove',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Remove Device'),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert,
                      color: AppTheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              if (deviceAlarms.isNotEmpty) ...[
                const SizedBox(height: AppTheme.spacingM),
                Wrap(
                  spacing: AppTheme.spacingS,
                  children: deviceAlarms.map((alarm) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: _getAlarmTypeColor(alarm.type).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusS),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getAlarmIcon(alarm.type),
                            size: 12,
                            color: _getAlarmTypeColor(alarm.type),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            alarm.typeDisplayName.split(
                                ' ')[0], // Just "Disconnect", "Connect", etc.
                            style: AppTheme.bodySmall.copyWith(
                              color: _getAlarmTypeColor(alarm.type),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1);
  }

  Color _getAlarmTypeColor(AlarmType type) {
    switch (type) {
      case AlarmType.disconnect:
        return AppTheme.error;
      case AlarmType.connect:
        return AppTheme.success;
      case AlarmType.proximity:
        return AppTheme.warning;
    }
  }

  IconData _getAlarmIcon(AlarmType type) {
    switch (type) {
      case AlarmType.disconnect:
        return Icons.bluetooth_disabled;
      case AlarmType.connect:
        return Icons.bluetooth_connected;
      case AlarmType.proximity:
        return Icons.near_me;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<void> _refreshData() async {
    final bluetoothService = context.read<BluetoothService>();
    // Refresh paired devices and saved devices
    await bluetoothService.initialize();
    await Future.delayed(const Duration(seconds: 1));
  }

  void _toggleBluetooth() async {
    final bluetoothService = context.read<BluetoothService>();
    if (bluetoothService.isBluetoothEnabled) {
      // Note: Cannot programmatically disable Bluetooth on Android
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please disable Bluetooth manually in system settings'),
        ),
      );
    } else {
      await bluetoothService.enableBluetooth();
    }
  }

  void _toggleMonitoring() async {
    final alarmService = context.read<AlarmService>();
    if (alarmService.isMonitoring) {
      await alarmService.stopMonitoring();
    } else {
      await alarmService.startMonitoring();
    }
  }

  void _navigateToScanScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BluetoothScanScreen(),
      ),
    );
  }

  void _navigateToAlarmSettings(BluetoothDeviceModel device) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AlarmSettingsScreen(device: device),
      ),
    );
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('App Settings'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to app settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('About'),
              onTap: () {
                Navigator.pop(context);
                _showAboutDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Bluetooth Alarm',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.bluetooth, size: 48),
      children: [
        const Text(
          'A smart alarm app that monitors your Bluetooth devices and alerts you when they connect or disconnect.',
        ),
      ],
    );
  }
}
