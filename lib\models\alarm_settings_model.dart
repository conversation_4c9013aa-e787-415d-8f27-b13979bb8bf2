enum AlarmType {
  disconnect,
  connect,
  proximity,
}

enum AlarmSound {
  defaultSound,
  beep,
  siren,
  bell,
  custom,
}

class AlarmSettings {
  final bool isEnabled;
  final AlarmType type;
  final AlarmSound sound;
  final String? customSoundPath;
  final double volume;
  final bool vibrate;
  final int vibrationPattern; // 0: short, 1: long, 2: pattern
  final bool snoozeEnabled;
  final int snoozeDuration; // in minutes
  final int maxSnoozeCount;
  final bool autoStopEnabled;
  final int autoStopDuration; // in minutes
  final String deviceAddress;
  final String deviceName;
  final DateTime createdAt;
  final DateTime? lastTriggered;

  AlarmSettings({
    this.isEnabled = true,
    required this.type,
    this.sound = AlarmSound.defaultSound,
    this.customSoundPath,
    this.volume = 0.8,
    this.vibrate = true,
    this.vibrationPattern = 0,
    this.snoozeEnabled = true,
    this.snoozeDuration = 5,
    this.maxSnoozeCount = 3,
    this.autoStopEnabled = true,
    this.autoStopDuration = 10,
    required this.deviceAddress,
    required this.deviceName,
    required this.createdAt,
    this.lastTriggered,
  });

  factory AlarmSettings.fromJson(Map<String, dynamic> json) {
    return AlarmSettings(
      isEnabled: json['isEnabled'] ?? true,
      type: AlarmType.values[json['type'] ?? 0],
      sound: AlarmSound.values[json['sound'] ?? 0],
      customSoundPath: json['customSoundPath'],
      volume: (json['volume'] ?? 0.8).toDouble(),
      vibrate: json['vibrate'] ?? true,
      vibrationPattern: json['vibrationPattern'] ?? 0,
      snoozeEnabled: json['snoozeEnabled'] ?? true,
      snoozeDuration: json['snoozeDuration'] ?? 5,
      maxSnoozeCount: json['maxSnoozeCount'] ?? 3,
      autoStopEnabled: json['autoStopEnabled'] ?? true,
      autoStopDuration: json['autoStopDuration'] ?? 10,
      deviceAddress: json['deviceAddress'],
      deviceName: json['deviceName'],
      createdAt: DateTime.parse(json['createdAt']),
      lastTriggered: json['lastTriggered'] != null 
          ? DateTime.parse(json['lastTriggered']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isEnabled': isEnabled,
      'type': type.index,
      'sound': sound.index,
      'customSoundPath': customSoundPath,
      'volume': volume,
      'vibrate': vibrate,
      'vibrationPattern': vibrationPattern,
      'snoozeEnabled': snoozeEnabled,
      'snoozeDuration': snoozeDuration,
      'maxSnoozeCount': maxSnoozeCount,
      'autoStopEnabled': autoStopEnabled,
      'autoStopDuration': autoStopDuration,
      'deviceAddress': deviceAddress,
      'deviceName': deviceName,
      'createdAt': createdAt.toIso8601String(),
      'lastTriggered': lastTriggered?.toIso8601String(),
    };
  }

  AlarmSettings copyWith({
    bool? isEnabled,
    AlarmType? type,
    AlarmSound? sound,
    String? customSoundPath,
    double? volume,
    bool? vibrate,
    int? vibrationPattern,
    bool? snoozeEnabled,
    int? snoozeDuration,
    int? maxSnoozeCount,
    bool? autoStopEnabled,
    int? autoStopDuration,
    String? deviceAddress,
    String? deviceName,
    DateTime? createdAt,
    DateTime? lastTriggered,
  }) {
    return AlarmSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      type: type ?? this.type,
      sound: sound ?? this.sound,
      customSoundPath: customSoundPath ?? this.customSoundPath,
      volume: volume ?? this.volume,
      vibrate: vibrate ?? this.vibrate,
      vibrationPattern: vibrationPattern ?? this.vibrationPattern,
      snoozeEnabled: snoozeEnabled ?? this.snoozeEnabled,
      snoozeDuration: snoozeDuration ?? this.snoozeDuration,
      maxSnoozeCount: maxSnoozeCount ?? this.maxSnoozeCount,
      autoStopEnabled: autoStopEnabled ?? this.autoStopEnabled,
      autoStopDuration: autoStopDuration ?? this.autoStopDuration,
      deviceAddress: deviceAddress ?? this.deviceAddress,
      deviceName: deviceName ?? this.deviceName,
      createdAt: createdAt ?? this.createdAt,
      lastTriggered: lastTriggered ?? this.lastTriggered,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case AlarmType.disconnect:
        return 'Disconnect Alarm';
      case AlarmType.connect:
        return 'Connect Alarm';
      case AlarmType.proximity:
        return 'Proximity Alarm';
    }
  }

  String get soundDisplayName {
    switch (sound) {
      case AlarmSound.defaultSound:
        return 'Default';
      case AlarmSound.beep:
        return 'Beep';
      case AlarmSound.siren:
        return 'Siren';
      case AlarmSound.bell:
        return 'Bell';
      case AlarmSound.custom:
        return 'Custom';
    }
  }

  String get vibrationDisplayName {
    switch (vibrationPattern) {
      case 0:
        return 'Short';
      case 1:
        return 'Long';
      case 2:
        return 'Pattern';
      default:
        return 'Short';
    }
  }

  String get id => '${deviceAddress}_${type.index}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AlarmSettings && 
           other.deviceAddress == deviceAddress && 
           other.type == type;
  }

  @override
  int get hashCode => Object.hash(deviceAddress, type);

  @override
  String toString() {
    return 'AlarmSettings(type: $type, device: $deviceName, enabled: $isEnabled)';
  }
}

class ActiveAlarm {
  final String id;
  final AlarmSettings settings;
  final DateTime triggeredAt;
  final int snoozeCount;
  final bool isActive;

  ActiveAlarm({
    required this.id,
    required this.settings,
    required this.triggeredAt,
    this.snoozeCount = 0,
    this.isActive = true,
  });

  ActiveAlarm copyWith({
    String? id,
    AlarmSettings? settings,
    DateTime? triggeredAt,
    int? snoozeCount,
    bool? isActive,
  }) {
    return ActiveAlarm(
      id: id ?? this.id,
      settings: settings ?? this.settings,
      triggeredAt: triggeredAt ?? this.triggeredAt,
      snoozeCount: snoozeCount ?? this.snoozeCount,
      isActive: isActive ?? this.isActive,
    );
  }

  bool get canSnooze => 
      settings.snoozeEnabled && 
      snoozeCount < settings.maxSnoozeCount;

  Duration get activeDuration => DateTime.now().difference(triggeredAt);

  bool get shouldAutoStop => 
      settings.autoStopEnabled && 
      activeDuration.inMinutes >= settings.autoStopDuration;
}
