import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/bluetooth_device_model.dart';
import 'storage_service.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  FlutterBluetoothSerial? _bluetooth;
  StreamSubscription<BluetoothDiscoveryResult>? _discoveryStreamSubscription;
  StreamSubscription<BluetoothState>? _bluetoothStateSubscription;

  bool _isScanning = false;
  bool _isBluetoothEnabled = false;
  final List<BluetoothDeviceModel> _discoveredDevices = [];
  final List<BluetoothDeviceModel> _pairedDevices = [];
  final List<SavedBluetoothDevice> _savedDevices = [];
  BluetoothDeviceModel? _connectedDevice;
  final StorageService _storageService = StorageService();

  // Getters
  bool get isScanning => _isScanning;
  bool get isBluetoothEnabled => _isBluetoothEnabled;
  List<BluetoothDeviceModel> get discoveredDevices =>
      List.unmodifiable(_discoveredDevices);
  List<BluetoothDeviceModel> get pairedDevices =>
      List.unmodifiable(_pairedDevices);
  List<SavedBluetoothDevice> get savedDevices =>
      List.unmodifiable(_savedDevices);
  BluetoothDeviceModel? get connectedDevice => _connectedDevice;

  // Initialize Bluetooth service
  Future<bool> initialize() async {
    try {
      _bluetooth = FlutterBluetoothSerial.instance;

      // Check initial Bluetooth state
      _isBluetoothEnabled = await _bluetooth!.isEnabled ?? false;

      // Listen to Bluetooth state changes
      _bluetoothStateSubscription =
          _bluetooth!.onStateChanged().listen((BluetoothState state) {
        _isBluetoothEnabled = state == BluetoothState.STATE_ON;
        notifyListeners();

        if (!_isBluetoothEnabled) {
          _discoveredDevices.clear();
          _connectedDevice = null;
          notifyListeners();
        }
      });

      // Load paired devices
      await _loadPairedDevices();

      // Load saved devices
      await _loadSavedDevices();

      return true;
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
      return false;
    }
  }

  // Request necessary permissions
  Future<bool> requestPermissions() async {
    try {
      Map<Permission, PermissionStatus> permissions = {};

      if (Platform.isAndroid) {
        permissions = await [
          Permission.bluetooth,
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.bluetoothAdvertise,
          Permission.location,
        ].request();
      }

      bool allGranted = permissions.values.every(
        (status) => status == PermissionStatus.granted,
      );

      return allGranted;
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return false;
    }
  }

  // Enable Bluetooth
  Future<bool> enableBluetooth() async {
    try {
      if (_bluetooth == null) return false;

      bool? result = await _bluetooth!.requestEnable();
      _isBluetoothEnabled = result ?? false;
      notifyListeners();

      return _isBluetoothEnabled;
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
      return false;
    }
  }

  // Load paired devices
  Future<void> _loadPairedDevices() async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return;

      List<BluetoothDevice> bondedDevices =
          await _bluetooth!.getBondedDevices();
      _pairedDevices.clear();
      _pairedDevices.addAll(bondedDevices
          .map((device) => BluetoothDeviceModel.fromBluetoothDevice(device))
          .toList());

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  // Load saved devices
  Future<void> _loadSavedDevices() async {
    try {
      List<SavedBluetoothDevice> savedDevices =
          await _storageService.getSavedDevices();
      _savedDevices.clear();
      _savedDevices.addAll(savedDevices);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
    }
  }

  // Start device discovery
  Future<void> startDiscovery() async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled || _isScanning) return;

      _isScanning = true;
      _discoveredDevices.clear();
      notifyListeners();

      _discoveryStreamSubscription = _bluetooth!.startDiscovery().listen(
        (BluetoothDiscoveryResult result) {
          BluetoothDeviceModel device =
              BluetoothDeviceModel.fromDiscoveryResult(result);

          // Update existing device or add new one
          int existingIndex = _discoveredDevices.indexWhere(
            (d) => d.address == device.address,
          );

          if (existingIndex != -1) {
            _discoveredDevices[existingIndex] = device;
          } else {
            _discoveredDevices.add(device);
          }

          notifyListeners();
        },
        onDone: () {
          _isScanning = false;
          notifyListeners();
        },
        onError: (error) {
          debugPrint('Discovery error: $error');
          _isScanning = false;
          notifyListeners();
        },
      );
    } catch (e) {
      debugPrint('Error starting discovery: $e');
      _isScanning = false;
      notifyListeners();
    }
  }

  // Stop device discovery
  Future<void> stopDiscovery() async {
    try {
      if (_bluetooth == null) return;

      await _bluetooth!.cancelDiscovery();
      await _discoveryStreamSubscription?.cancel();
      _discoveryStreamSubscription = null;
      _isScanning = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error stopping discovery: $e');
    }
  }

  // Pair with device
  Future<bool> pairDevice(BluetoothDeviceModel device) async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return false;

      bool? result = await FlutterBluetoothSerial.instance
          .bondDeviceAtAddress(device.address);

      if (result == true) {
        await _loadPairedDevices();

        // Save device to saved devices list
        await _saveDeviceToSavedList(device);

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error pairing device: $e');
      return false;
    }
  }

  // Connect to device
  Future<bool> connectToDevice(BluetoothDeviceModel device) async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return false;

      // For this implementation, we'll simulate connection
      // In a real app, you'd establish an actual Bluetooth connection
      _connectedDevice = device.copyWith(isConnected: true);
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      return false;
    }
  }

  // Disconnect from device
  Future<void> disconnectDevice() async {
    try {
      if (_connectedDevice != null) {
        _connectedDevice = null;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error disconnecting device: $e');
    }
  }

  // Check if device is in range (improved implementation)
  Future<bool> isDeviceInRange(String deviceAddress) async {
    try {
      // Check if device is in discovered devices (recently seen)
      BluetoothDeviceModel? discoveredDevice = _discoveredDevices
          .where((device) => device.address == deviceAddress)
          .firstOrNull;

      if (discoveredDevice != null) {
        // Check if device was seen recently (within last 30 seconds)
        if (discoveredDevice.lastSeen != null) {
          Duration timeSinceLastSeen =
              DateTime.now().difference(discoveredDevice.lastSeen!);
          return timeSinceLastSeen.inSeconds <= 30;
        }
        return true;
      }

      // Check if device is currently connected
      if (_connectedDevice?.address == deviceAddress) {
        return _connectedDevice!.isConnected;
      }

      // Try to ping the device by checking if it's still bonded
      BluetoothDeviceModel? pairedDevice = _pairedDevices
          .where((device) => device.address == deviceAddress)
          .firstOrNull;

      if (pairedDevice != null && pairedDevice.isPaired) {
        // For paired devices, we assume they're in range if recently discovered
        // In a real implementation, you might try to establish a connection
        return false; // Conservative approach - assume out of range if not recently seen
      }

      return false;
    } catch (e) {
      debugPrint('Error checking device range: $e');
      return false;
    }
  }

  // Get device signal strength
  int? getDeviceSignalStrength(String deviceAddress) {
    try {
      BluetoothDeviceModel? device = _discoveredDevices
          .where((device) => device.address == deviceAddress)
          .firstOrNull;
      return device?.rssi;
    } catch (e) {
      debugPrint('Error getting device signal strength: $e');
      return null;
    }
  }

  // Get device by address
  BluetoothDeviceModel? getDeviceByAddress(String address) {
    try {
      return _pairedDevices.firstWhere((device) => device.address == address);
    } catch (e) {
      try {
        return _discoveredDevices
            .firstWhere((device) => device.address == address);
      } catch (e) {
        return null;
      }
    }
  }

  // Save device to saved devices list
  Future<void> _saveDeviceToSavedList(BluetoothDeviceModel device) async {
    try {
      SavedBluetoothDevice savedDevice = SavedBluetoothDevice(
        address: device.address,
        name: device.displayName,
        savedAt: DateTime.now(),
        alarmOnDisconnect: true, // Default to enabled
        alarmOnConnect: false,
      );

      // Check if device already exists
      int existingIndex = _savedDevices.indexWhere(
        (saved) => saved.address == device.address,
      );

      if (existingIndex != -1) {
        _savedDevices[existingIndex] = savedDevice;
      } else {
        _savedDevices.add(savedDevice);
      }

      await _storageService.saveSavedDevices(_savedDevices);
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving device to saved list: $e');
    }
  }

  // Remove device from saved list
  Future<void> removeSavedDevice(String deviceAddress) async {
    try {
      _savedDevices.removeWhere((device) => device.address == deviceAddress);
      await _storageService.saveSavedDevices(_savedDevices);
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing saved device: $e');
    }
  }

  // Update saved device settings
  Future<void> updateSavedDevice(SavedBluetoothDevice updatedDevice) async {
    try {
      int index = _savedDevices.indexWhere(
        (device) => device.address == updatedDevice.address,
      );

      if (index != -1) {
        _savedDevices[index] = updatedDevice;
        await _storageService.saveSavedDevices(_savedDevices);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating saved device: $e');
    }
  }

  // Get saved device by address
  SavedBluetoothDevice? getSavedDeviceByAddress(String address) {
    try {
      return _savedDevices.firstWhere((device) => device.address == address);
    } catch (e) {
      return null;
    }
  }

  // Dispose resources
  @override
  void dispose() {
    _discoveryStreamSubscription?.cancel();
    _bluetoothStateSubscription?.cancel();
    super.dispose();
  }
}
