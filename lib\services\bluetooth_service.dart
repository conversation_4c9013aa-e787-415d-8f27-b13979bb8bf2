import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/bluetooth_device_model.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  FlutterBluetoothSerial? _bluetooth;
  StreamSubscription<BluetoothDiscoveryResult>? _discoveryStreamSubscription;
  StreamSubscription<BluetoothState>? _bluetoothStateSubscription;

  bool _isScanning = false;
  bool _isBluetoothEnabled = false;
  List<BluetoothDeviceModel> _discoveredDevices = [];
  List<BluetoothDeviceModel> _pairedDevices = [];
  BluetoothDeviceModel? _connectedDevice;

  // Getters
  bool get isScanning => _isScanning;
  bool get isBluetoothEnabled => _isBluetoothEnabled;
  List<BluetoothDeviceModel> get discoveredDevices =>
      List.unmodifiable(_discoveredDevices);
  List<BluetoothDeviceModel> get pairedDevices =>
      List.unmodifiable(_pairedDevices);
  BluetoothDeviceModel? get connectedDevice => _connectedDevice;

  // Initialize Bluetooth service
  Future<bool> initialize() async {
    try {
      _bluetooth = FlutterBluetoothSerial.instance;

      // Check initial Bluetooth state
      _isBluetoothEnabled = await _bluetooth!.isEnabled ?? false;

      // Listen to Bluetooth state changes
      _bluetoothStateSubscription =
          _bluetooth!.onStateChanged().listen((BluetoothState state) {
        _isBluetoothEnabled = state == BluetoothState.STATE_ON;
        notifyListeners();

        if (!_isBluetoothEnabled) {
          _discoveredDevices.clear();
          _connectedDevice = null;
          notifyListeners();
        }
      });

      // Load paired devices
      await _loadPairedDevices();

      return true;
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
      return false;
    }
  }

  // Request necessary permissions
  Future<bool> requestPermissions() async {
    try {
      Map<Permission, PermissionStatus> permissions = {};

      if (Platform.isAndroid) {
        permissions = await [
          Permission.bluetooth,
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.bluetoothAdvertise,
          Permission.location,
        ].request();
      }

      bool allGranted = permissions.values.every(
        (status) => status == PermissionStatus.granted,
      );

      return allGranted;
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return false;
    }
  }

  // Enable Bluetooth
  Future<bool> enableBluetooth() async {
    try {
      if (_bluetooth == null) return false;

      bool? result = await _bluetooth!.requestEnable();
      _isBluetoothEnabled = result ?? false;
      notifyListeners();

      return _isBluetoothEnabled;
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
      return false;
    }
  }

  // Load paired devices
  Future<void> _loadPairedDevices() async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return;

      List<BluetoothDevice> bondedDevices =
          await _bluetooth!.getBondedDevices();
      _pairedDevices = bondedDevices
          .map((device) => BluetoothDeviceModel.fromBluetoothDevice(device))
          .toList();

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  // Start device discovery
  Future<void> startDiscovery() async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled || _isScanning) return;

      _isScanning = true;
      _discoveredDevices.clear();
      notifyListeners();

      _discoveryStreamSubscription = _bluetooth!.startDiscovery().listen(
        (BluetoothDiscoveryResult result) {
          BluetoothDeviceModel device =
              BluetoothDeviceModel.fromDiscoveryResult(result);

          // Update existing device or add new one
          int existingIndex = _discoveredDevices.indexWhere(
            (d) => d.address == device.address,
          );

          if (existingIndex != -1) {
            _discoveredDevices[existingIndex] = device;
          } else {
            _discoveredDevices.add(device);
          }

          notifyListeners();
        },
        onDone: () {
          _isScanning = false;
          notifyListeners();
        },
        onError: (error) {
          debugPrint('Discovery error: $error');
          _isScanning = false;
          notifyListeners();
        },
      );
    } catch (e) {
      debugPrint('Error starting discovery: $e');
      _isScanning = false;
      notifyListeners();
    }
  }

  // Stop device discovery
  Future<void> stopDiscovery() async {
    try {
      if (_bluetooth == null) return;

      await _bluetooth!.cancelDiscovery();
      await _discoveryStreamSubscription?.cancel();
      _discoveryStreamSubscription = null;
      _isScanning = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error stopping discovery: $e');
    }
  }

  // Pair with device
  Future<bool> pairDevice(BluetoothDeviceModel device) async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return false;

      bool? result = await FlutterBluetoothSerial.instance
          .bondDeviceAtAddress(device.address);

      if (result == true) {
        await _loadPairedDevices();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error pairing device: $e');
      return false;
    }
  }

  // Connect to device
  Future<bool> connectToDevice(BluetoothDeviceModel device) async {
    try {
      if (_bluetooth == null || !_isBluetoothEnabled) return false;

      // For this implementation, we'll simulate connection
      // In a real app, you'd establish an actual Bluetooth connection
      _connectedDevice = device.copyWith(isConnected: true);
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      return false;
    }
  }

  // Disconnect from device
  Future<void> disconnectDevice() async {
    try {
      if (_connectedDevice != null) {
        _connectedDevice = null;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error disconnecting device: $e');
    }
  }

  // Check if device is in range (simplified implementation)
  Future<bool> isDeviceInRange(String deviceAddress) async {
    try {
      // In a real implementation, you would check RSSI or connection status
      // For now, we'll simulate this based on discovered devices
      return _discoveredDevices
          .any((device) => device.address == deviceAddress);
    } catch (e) {
      debugPrint('Error checking device range: $e');
      return false;
    }
  }

  // Get device by address
  BluetoothDeviceModel? getDeviceByAddress(String address) {
    try {
      return _pairedDevices.firstWhere((device) => device.address == address);
    } catch (e) {
      try {
        return _discoveredDevices
            .firstWhere((device) => device.address == address);
      } catch (e) {
        return null;
      }
    }
  }

  // Dispose resources
  @override
  void dispose() {
    _discoveryStreamSubscription?.cancel();
    _bluetoothStateSubscription?.cancel();
    super.dispose();
  }
}
