import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';
import '../models/bluetooth_device_model.dart';

class DeviceCard extends StatelessWidget {
  final BluetoothDeviceModel device;
  final VoidCallback? onTap;
  final VoidCallback? onConnect;
  final VoidCallback? onDisconnect;
  final bool showActions;

  const DeviceCard({
    super.key,
    required this.device,
    this.onTap,
    this.onConnect,
    this.onDisconnect,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildDeviceIcon(),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          device.displayName,
                          style: AppTheme.labelLarge,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppTheme.spacingXS),
                        Text(
                          device.address,
                          style: AppTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  _buildConnectionStatus(),
                ],
              ),
              if (device.rssi != null || device.lastSeen != null) ...[
                const SizedBox(height: AppTheme.spacingM),
                _buildDeviceInfo(),
              ],
              if (showActions) ...[
                const SizedBox(height: AppTheme.spacingM),
                _buildActionButtons(),
              ],
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1);
  }

  Widget _buildDeviceIcon() {
    IconData iconData;
    Color iconColor;

    if (device.isConnected) {
      iconData = Icons.bluetooth_connected;
      iconColor = AppTheme.success;
    } else if (device.isPaired) {
      iconData = Icons.bluetooth;
      iconColor = AppTheme.primaryBlue;
    } else {
      iconData = Icons.bluetooth_searching;
      iconColor = AppTheme.onSurfaceVariant;
    }

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingS),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Widget _buildConnectionStatus() {
    Color statusColor;
    String statusText = device.connectionStatus;

    switch (statusText) {
      case 'Connected':
        statusColor = AppTheme.success;
        break;
      case 'Paired':
        statusColor = AppTheme.primaryBlue;
        break;
      default:
        statusColor = AppTheme.onSurfaceVariant;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: AppTheme.bodySmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDeviceInfo() {
    return Row(
      children: [
        if (device.rssi != null) ...[
          Icon(
            Icons.signal_cellular_alt,
            size: 16,
            color: AppTheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppTheme.spacingXS),
          Text(
            '${device.rssi} dBm (${device.signalStrength})',
            style: AppTheme.bodySmall,
          ),
        ],
        if (device.rssi != null && device.lastSeen != null)
          const SizedBox(width: AppTheme.spacingM),
        if (device.lastSeen != null) ...[
          Icon(
            Icons.access_time,
            size: 16,
            color: AppTheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppTheme.spacingXS),
          Text(
            _formatLastSeen(device.lastSeen!),
            style: AppTheme.bodySmall,
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (!device.isPaired)
          TextButton.icon(
            onPressed: onConnect,
            icon: const Icon(Icons.link, size: 16),
            label: const Text('Pair'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.primaryBlue,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingM,
                vertical: AppTheme.spacingS,
              ),
            ),
          )
        else if (!device.isConnected)
          TextButton.icon(
            onPressed: onConnect,
            icon: const Icon(Icons.bluetooth_connected, size: 16),
            label: const Text('Connect'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.success,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingM,
                vertical: AppTheme.spacingS,
              ),
            ),
          )
        else
          TextButton.icon(
            onPressed: onDisconnect,
            icon: const Icon(Icons.bluetooth_disabled, size: 16),
            label: const Text('Disconnect'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.error,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingM,
                vertical: AppTheme.spacingS,
              ),
            ),
          ),
        const SizedBox(width: AppTheme.spacingS),
        IconButton(
          onPressed: onTap,
          icon: const Icon(Icons.settings),
          iconSize: 20,
          color: AppTheme.onSurfaceVariant,
          tooltip: 'Alarm Settings',
        ),
      ],
    );
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class DeviceListTile extends StatelessWidget {
  final BluetoothDeviceModel device;
  final VoidCallback? onTap;
  final Widget? trailing;

  const DeviceListTile({
    super.key,
    required this.device,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: _buildDeviceIcon(),
      title: Text(
        device.displayName,
        style: AppTheme.labelLarge,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            device.address,
            style: AppTheme.bodySmall,
          ),
          if (device.rssi != null)
            Text(
              'Signal: ${device.signalStrength} (${device.rssi} dBm)',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
      trailing: trailing ?? _buildConnectionBadge(),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingM,
        vertical: AppTheme.spacingS,
      ),
    );
  }

  Widget _buildDeviceIcon() {
    IconData iconData;
    Color iconColor;

    if (device.isConnected) {
      iconData = Icons.bluetooth_connected;
      iconColor = AppTheme.success;
    } else if (device.isPaired) {
      iconData = Icons.bluetooth;
      iconColor = AppTheme.primaryBlue;
    } else {
      iconData = Icons.bluetooth_searching;
      iconColor = AppTheme.onSurfaceVariant;
    }

    return Icon(iconData, color: iconColor);
  }

  Widget _buildConnectionBadge() {
    Color statusColor;
    String statusText = device.connectionStatus;

    switch (statusText) {
      case 'Connected':
        statusColor = AppTheme.success;
        break;
      case 'Paired':
        statusColor = AppTheme.primaryBlue;
        break;
      default:
        statusColor = AppTheme.onSurfaceVariant;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        statusText,
        style: AppTheme.bodySmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
