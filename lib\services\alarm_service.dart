import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:vibration/vibration.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/alarm_settings_model.dart';
import '../models/bluetooth_device_model.dart';
import 'bluetooth_service.dart';
import 'storage_service.dart';

class AlarmService extends ChangeNotifier {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  final BluetoothService _bluetoothService = BluetoothService();
  final StorageService _storageService = StorageService();

  Timer? _monitoringTimer;
  Timer? _alarmTimer;
  List<AlarmSettings> _alarmSettings = [];
  List<ActiveAlarm> _activeAlarms = [];
  bool _isMonitoring = false;

  // Getters
  List<AlarmSettings> get alarmSettings => List.unmodifiable(_alarmSettings);
  List<ActiveAlarm> get activeAlarms => List.unmodifiable(_activeAlarms);
  bool get isMonitoring => _isMonitoring;
  bool get hasActiveAlarms => _activeAlarms.isNotEmpty;

  // Initialize alarm service
  Future<bool> initialize() async {
    try {
      // Initialize notifications
      await _initializeNotifications();

      // Load saved alarm settings
      await _loadAlarmSettings();

      // Start monitoring if there are active alarms
      if (_alarmSettings.any((alarm) => alarm.isEnabled)) {
        await startMonitoring();
      }

      return true;
    } catch (e) {
      debugPrint('Error initializing alarm service: $e');
      return false;
    }
  }

  // Initialize notifications
  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request notification permissions
    if (Platform.isAndroid) {
      await _notifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap - could navigate to alarm screen
    debugPrint('Notification tapped: ${response.payload}');
  }

  // Load alarm settings from storage
  Future<void> _loadAlarmSettings() async {
    try {
      _alarmSettings = await _storageService.getAlarmSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
    }
  }

  // Save alarm settings
  Future<void> saveAlarmSettings(AlarmSettings settings) async {
    try {
      // Update or add alarm settings
      int existingIndex = _alarmSettings.indexWhere(
        (alarm) =>
            alarm.deviceAddress == settings.deviceAddress &&
            alarm.type == settings.type,
      );

      if (existingIndex != -1) {
        _alarmSettings[existingIndex] = settings;
      } else {
        _alarmSettings.add(settings);
      }

      await _storageService.saveAlarmSettings(_alarmSettings);
      notifyListeners();

      // Restart monitoring if needed
      if (settings.isEnabled && !_isMonitoring) {
        await startMonitoring();
      }
    } catch (e) {
      debugPrint('Error saving alarm settings: $e');
    }
  }

  // Delete alarm settings
  Future<void> deleteAlarmSettings(String deviceAddress, AlarmType type) async {
    try {
      _alarmSettings.removeWhere(
        (alarm) => alarm.deviceAddress == deviceAddress && alarm.type == type,
      );

      await _storageService.saveAlarmSettings(_alarmSettings);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting alarm settings: $e');
    }
  }

  // Start monitoring for alarm conditions
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    notifyListeners();

    // Monitor every 10 seconds for better battery life
    _monitoringTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkAlarmConditions();
    });

    debugPrint('Alarm monitoring started');
  }

  // Stop monitoring
  Future<void> stopMonitoring() async {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    notifyListeners();

    debugPrint('Alarm monitoring stopped');
  }

  // Check alarm conditions
  Future<void> _checkAlarmConditions() async {
    try {
      for (AlarmSettings settings in _alarmSettings) {
        if (!settings.isEnabled) continue;

        BluetoothDeviceModel? device =
            _bluetoothService.getDeviceByAddress(settings.deviceAddress);
        if (device == null) continue;

        bool shouldTrigger = false;
        String triggerReason = '';

        bool isInRange =
            await _bluetoothService.isDeviceInRange(settings.deviceAddress);

        switch (settings.type) {
          case AlarmType.disconnect:
            // Only trigger if device was previously in range and now isn't
            if (!isInRange && !_isAlarmActive(settings.id)) {
              shouldTrigger = true;
              triggerReason =
                  '${settings.deviceName} disconnected or out of range';
            }
            break;

          case AlarmType.connect:
            // Trigger when device comes into range
            if (isInRange && !_isAlarmActive(settings.id)) {
              shouldTrigger = true;
              triggerReason = '${settings.deviceName} connected or in range';
            }
            break;

          case AlarmType.proximity:
            // Trigger based on signal strength
            int? rssi = _bluetoothService
                .getDeviceSignalStrength(settings.deviceAddress);
            if (rssi != null && rssi > -60 && !_isAlarmActive(settings.id)) {
              shouldTrigger = true;
              triggerReason =
                  '${settings.deviceName} is nearby (signal: ${rssi}dBm)';
            }
            break;
        }

        if (shouldTrigger) {
          await _triggerAlarm(settings, triggerReason);
        }
      }

      // Check for auto-stop alarms
      _checkAutoStopAlarms();
    } catch (e) {
      debugPrint('Error checking alarm conditions: $e');
    }
  }

  // Check if alarm is already active
  bool _isAlarmActive(String alarmId) {
    return _activeAlarms.any((alarm) => alarm.id == alarmId && alarm.isActive);
  }

  // Trigger alarm
  Future<void> _triggerAlarm(AlarmSettings settings, String reason) async {
    try {
      ActiveAlarm activeAlarm = ActiveAlarm(
        id: settings.id,
        settings: settings,
        triggeredAt: DateTime.now(),
      );

      _activeAlarms.add(activeAlarm);
      notifyListeners();

      // Update last triggered time
      AlarmSettings updatedSettings =
          settings.copyWith(lastTriggered: DateTime.now());
      await saveAlarmSettings(updatedSettings);

      // Play alarm sound
      await _playAlarmSound(settings);

      // Vibrate if enabled
      if (settings.vibrate) {
        await _startVibration(settings);
      }

      // Show notification
      await _showAlarmNotification(settings, reason);

      debugPrint(
          'Alarm triggered: ${settings.typeDisplayName} for ${settings.deviceName}');
    } catch (e) {
      debugPrint('Error triggering alarm: $e');
    }
  }

  // Play alarm sound
  Future<void> _playAlarmSound(AlarmSettings settings) async {
    try {
      String soundPath;

      switch (settings.sound) {
        case AlarmSound.defaultSound:
          soundPath = 'sounds/default_alarm.mp3';
          break;
        case AlarmSound.beep:
          soundPath = 'sounds/beep.mp3';
          break;
        case AlarmSound.siren:
          soundPath = 'sounds/siren.mp3';
          break;
        case AlarmSound.bell:
          soundPath = 'sounds/bell.mp3';
          break;
        case AlarmSound.custom:
          soundPath = settings.customSoundPath ?? 'sounds/default_alarm.mp3';
          break;
      }

      await _audioPlayer.setVolume(settings.volume);
      await _audioPlayer.setReleaseMode(ReleaseMode.loop);
      await _audioPlayer.play(AssetSource(soundPath));
    } catch (e) {
      debugPrint('Error playing alarm sound: $e');
      // Fallback to system sound
      SystemSound.play(SystemSoundType.alert);
    }
  }

  // Start vibration
  Future<void> _startVibration(AlarmSettings settings) async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        switch (settings.vibrationPattern) {
          case 0: // Short
            Vibration.vibrate(duration: 500);
            break;
          case 1: // Long
            Vibration.vibrate(duration: 2000);
            break;
          case 2: // Pattern
            Vibration.vibrate(pattern: [0, 500, 200, 500, 200, 500]);
            break;
        }
      }
    } catch (e) {
      debugPrint('Error starting vibration: $e');
    }
  }

  // Show alarm notification
  Future<void> _showAlarmNotification(
      AlarmSettings settings, String reason) async {
    try {
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'bluetooth_alarm_channel',
        'Bluetooth Alarms',
        channelDescription: 'Notifications for Bluetooth device alarms',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        ongoing: true,
      );

      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidDetails);

      await _notifications.show(
        settings.id.hashCode,
        'Bluetooth Alarm',
        '$reason - ${settings.deviceName}',
        notificationDetails,
        payload: settings.id,
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  // Stop alarm
  Future<void> stopAlarm(String alarmId) async {
    try {
      _activeAlarms.removeWhere((alarm) => alarm.id == alarmId);
      notifyListeners();

      await _audioPlayer.stop();
      await Vibration.cancel();
      await _notifications.cancel(alarmId.hashCode);

      debugPrint('Alarm stopped: $alarmId');
    } catch (e) {
      debugPrint('Error stopping alarm: $e');
    }
  }

  // Snooze alarm
  Future<void> snoozeAlarm(String alarmId) async {
    try {
      int alarmIndex = _activeAlarms.indexWhere((alarm) => alarm.id == alarmId);
      if (alarmIndex == -1) return;

      ActiveAlarm alarm = _activeAlarms[alarmIndex];
      if (!alarm.canSnooze) return;

      // Stop current alarm
      await _audioPlayer.stop();
      await Vibration.cancel();
      await _notifications.cancel(alarmId.hashCode);

      // Update snooze count
      _activeAlarms[alarmIndex] = alarm.copyWith(
        snoozeCount: alarm.snoozeCount + 1,
        isActive: false,
      );
      notifyListeners();

      // Schedule alarm to trigger again after snooze duration
      Timer(Duration(minutes: alarm.settings.snoozeDuration), () {
        if (_activeAlarms.any((a) => a.id == alarmId)) {
          _triggerAlarm(alarm.settings, 'Snoozed alarm');
        }
      });

      debugPrint('Alarm snoozed: $alarmId');
    } catch (e) {
      debugPrint('Error snoozing alarm: $e');
    }
  }

  // Check for auto-stop alarms
  void _checkAutoStopAlarms() {
    List<ActiveAlarm> alarmsToStop =
        _activeAlarms.where((alarm) => alarm.shouldAutoStop).toList();

    for (ActiveAlarm alarm in alarmsToStop) {
      stopAlarm(alarm.id);
    }
  }

  // Stop all alarms
  Future<void> stopAllAlarms() async {
    try {
      for (ActiveAlarm alarm in _activeAlarms) {
        await stopAlarm(alarm.id);
      }
    } catch (e) {
      debugPrint('Error stopping all alarms: $e');
    }
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    _alarmTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }
}
